"""
Simulates reaction mechanisms using Nudged Elastic Band (NEB).
"""
from ase import Atoms
from ase.neb import NEB
from ase.optimize import BFGS
from pyscf import gto, dft
from ase.calculators.pyscf import PySCF as ASEPySCF

def setup_neb_calculation(initial_state: Atoms, final_state: Atoms, num_images: int = 5) -> list[Atoms]:
    """
    Creates an initial series of images for a NEB calculation by linear interpolation.

    Args:
        initial_state: The optimized ASE Atoms object for the reactants.
        final_state: The optimized ASE Atoms object for the products.
        num_images: The number of intermediate images to create.

    Returns:
        A list of ASE Atoms objects representing the initial NEB path.
    """
    initial_image = initial_state.copy()
    final_image = final_state.copy()

    images = [initial_image]
    for i in range(num_images):
        image = initial_image.copy()
        # Linearly interpolate positions between initial and final states
        image.set_positions((1 - (i + 1) / (num_images + 1)) * initial_state.get_positions() + \
                            ((i + 1) / (num_images + 1)) * final_state.get_positions())
        images.append(image)
    images.append(final_image)

    return images

def run_neb(images: list[Atoms], xc: str, basis: str, solvent: str = None) -> NEB:
    """
    Runs a NEB calculation to find the minimum energy path.

    Args:
        images: A list of ASE Atoms objects for the NEB path.
        xc: The exchange-correlation functional to use.
        basis: The basis set for the DFT calculation.
        solvent: The name of the implicit solvent model to use.

    Returns:
        The converged NEB object.
    """
    if not images or len(images) < 3:
        raise ValueError("NEB requires at least 3 images (initial, one intermediate, final).")

    print(f"\nRunning NEB calculation with {len(images)} images using xc='{xc}', basis='{basis}'...")

    # Set up and attach the PySCF calculator for each intermediate image
    for image in images[1:-1]:
        mol = gto.Mole()
        mol.atom = [[atom.symbol, atom.position] for atom in image]
        mol.basis = basis
        mol.build()

        mf = dft.RKS(mol)
        mf.xc = xc

        # Add solvent model if specified
        if solvent:
            mf = mf.with_solvent({'solvent_model': solvent})
        
        # ASE's NEB implementation requires a calculator instance on each image
        image.calc = ASEPySCF(mf=mf)

    # Create the NEB object. allow_shared_calculator must be False as each image
    # has a distinct geometry and thus a distinct PySCF calculator object.
    neb = NEB(images, allow_shared_calculator=False)

    # Use an optimizer to relax the NEB path to the minimum energy path
    optimizer = BFGS(neb, trajectory='neb.traj', logfile='neb.log')
    try:
        # Using a slightly looser convergence criterion for demonstration purposes
        optimizer.run(fmax=0.1) 
        print("NEB calculation converged.")
    except Exception as e:
        print(f"Warning: NEB calculation did not fully converge. Error: {e}")

    return neb