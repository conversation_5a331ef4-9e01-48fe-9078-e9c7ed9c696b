"""
Performs DFT-level geometry optimization and vibrational analysis.
"""
from ase import Atoms
from ase.optimize import BFGS
from ase.vibrations import Vibrations
from pyscf import gto, dft
from ase.calculators.pyscf import PySCF as ASEPySCF

def optimize_geometry(atoms: Atoms, xc: str, basis: str, solvent: str = None) -> Atoms:
    """
    Optimizes the geometry of an ASE Atoms object using PySCF.

    Args:
        atoms: The ASE Atoms object of the molecule to be optimized.
        xc: The exchange-correlation functional to use.
        basis: The basis set to use for the DFT calculation.
        solvent: The name of the implicit solvent model to use (e.g., 'Water').

    Returns:
        The optimized ASE Atoms object with the calculator attached.
    """
    if not isinstance(atoms, Atoms):
        raise TypeError("Input must be an ASE Atoms object.")

    print(f"Optimizing geometry for {atoms.get_chemical_formula()} with xc='{xc}', basis='{basis}'...")

    # Set up the PySCF molecule
    mol = gto.Mole()
    mol.atom = [[atom.symbol, atom.position] for atom in atoms]
    mol.basis = basis
    mol.build()

    # Set up the DFT method
    mf = dft.RKS(mol)
    mf.xc = xc
    
    # Add solvent model if specified
    if solvent:
        print(f"Applying implicit solvent model (PCM): {solvent}")
        mf = mf.with_solvent({'solvent_model': solvent})

    # Custom ASE-compatible calculator class to handle PySCF integration
    class CustomPySCF(ASEPySCF):
        def __init__(self, mf_instance, **kwargs):
            super().__init__(**kwargs)
            # This mf object is the master one with all settings
            self.mf = mf_instance
        
        def calculate(self, atoms=None, properties=['energy'], system_changes=...):
            # Update the geometry for the current step
            self.mf.mol.atom = [[atom.symbol, atom.position] for atom in atoms]
            self.mf.mol.build(False, False)
            
            # Call the base ASEPySCF calculate method
            super().calculate(atoms, properties, system_changes)
            
            # Run the PySCF calculation and store results
            self.results['energy'] = self.mf.kernel()
            g = self.mf.nuc_grad_method()
            self.results['forces'] = -g.kernel() # ASE expects forces (negative gradient)

    # Attach the fully configured calculator to the atoms object
    atoms.calc = CustomPySCF(mf_instance=mf)

    # Run the geometry optimization
    optimizer = BFGS(atoms, trajectory=f'opt_{atoms.get_chemical_formula()}.traj', logfile=f'opt_{atoms.get_chemical_formula()}.log')
    try:
        optimizer.run(fmax=0.05)
        print(f"Optimization converged for {atoms.get_chemical_formula()}.")
    except Exception as e:
        print(f"Warning: Geometry optimization did not converge for {atoms.get_chemical_formula()}. Error: {e}")

    # The atoms object is updated in-place and has the calculator attached
    return atoms

def calculate_thermo_data(atoms: Atoms) -> Vibrations:
    """
    Performs a vibrational frequency analysis on an optimized molecule.
    It uses the calculator already attached to the atoms object.

    Args:
        atoms: An optimized ASE Atoms object with a calculator attached.

    Returns:
        An ASE Vibrations object containing the frequency data.
    """
    if not hasattr(atoms, 'calc'):
        raise ValueError("The Atoms object must have a calculator attached for vibrational analysis.")
    
    print(f"Running vibrational analysis for {atoms.get_chemical_formula()}...")
    try:
        vib = Vibrations(atoms, name=f"vib_{atoms.get_chemical_formula()}")
        vib.run()
        print(f"Vibrational analysis successful. Summary:")
        vib.summary()
        return vib
    except Exception as e:
        print(f"Error during vibrational analysis for {atoms.get_chemical_formula()}: {e}")
        return None