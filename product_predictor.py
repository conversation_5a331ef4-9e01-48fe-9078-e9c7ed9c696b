"""
Predicts the most likely product(s) of a reaction using different engines.
- Transformer: A deep learning model trained for chemical reactions.
- RDKit: A template-based approach using reaction SMARTS.
"""
import sys
from rdkit import Chem
from rdkit.Chem import AllChem

# --- AI-based Predictor using Hugging Face Transformers ---

def predict_products_with_transformer(reactant_smiles: list[str]) -> list[str]:
    """
    Predicts reaction products using <PERSON><PERSON><PERSON>'s Molecular Transformer.

    Args:
        reactant_smiles: A list of SMILES strings for the reactants.

    Returns:
        A list of SMILES strings for the predicted products, or an empty list on failure.
    """
    try:
        from transformers import AutoTokenizer, AutoModelForSeq2SeqLM
    except ImportError:
        print("\nERROR: The 'transformers' or 'torch' library is not installed.", file=sys.stderr)
        print("Please install them with: pip install transformers torch", file=sys.stderr)
        return []

    print("Loading AI model for product prediction (may take a moment)...")
    try:
        # Using the recommended forward prediction model
        model_name = "pschwllr/fast-forward-prediction-level-2"
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForSeq2SeqLM.from_pretrained(model_name)
    except Exception as e:
        print(f"\nERROR: Could not load the transformer model. Check your internet connection. Details: {e}", file=sys.stderr)
        return []

    # Format the input SMILES as required by the model: "Reactant1.Reactant2>>"
    reaction_input = ".".join(reactant_smiles) + ">>"
    
    print(f"Querying AI model with input: {reaction_input}")
    
    # Tokenize and generate the prediction
    input_ids = tokenizer(reaction_input, return_tensors="pt").input_ids
    output_ids = model.generate(input_ids, max_length=256, num_beams=5, early_stopping=True)
    
    # Decode the output and clean it up
    predicted_smiles = tokenizer.decode(output_ids[0], skip_special_tokens=True)
    
    # Final check to ensure the output is a valid molecule
    if Chem.MolFromSmiles(predicted_smiles):
        print(f"AI model predicted product: {predicted_smiles}")
        return [predicted_smiles]
    else:
        print(f"Warning: AI model produced an invalid SMILES string: {predicted_smiles}", file=sys.stderr)
        return []


# --- Fallback Predictor using RDKit Templates ---

def predict_products_with_rdkit(reactant_smiles: list[str]) -> list[str]:
    """
    Predicts products using a predefined library of RDKit reaction templates.
    """
    if len(reactant_smiles) < 1:
        return []

    reaction_templates = {
        "diels_alder": "[C:1]=[C:2]-[C:3]=[C:4].[C:5]=[C:6]>>[C:1]1-[C:2]=[C:3]-[C:4]-[C:5]-[C:6]-1",
        "esterification": "[C:1](=[O:2])-[OD1].[O:3]-[C:4]>>[C:1](=[O:2])-[O:3]-[C:4]",
        # HCN Isomerization
        "isomerization_hcn": "[C-0:1]#[N+1:2]-[H:3]>>[C-0:1](-[H:3])#[N+0:2]",
    }

    reacts = tuple(Chem.MolFromSmiles(s) for s in reactant_smiles)
    if any(mol is None for mol in reacts):
        print("Error: RDKit could not parse all reactant SMILES.", file=sys.stderr)
        return []

    for name, smarts in reaction_templates.items():
        rxn = AllChem.ReactionFromSmarts(smarts)
        products = rxn.RunReactants(reacts)

        if products:
            print(f"Reactants match the RDKit template: '{name}'")
            product_smiles = [Chem.MolToSMILES(mol) for mol in products[0]]
            return product_smiles
    
    return []


# --- Main Dispatcher Function ---

def predict_products(reactant_smiles: list[str], method: str = 'transformer') -> list[str]:
    """
    Main dispatcher to select the prediction engine.

    Args:
        reactant_smiles: List of reactant SMILES strings.
        method: The engine to use ('transformer' or 'rdkit').

    Returns:
        A list of predicted product SMILES strings.
    """
    if method == 'transformer':
        return predict_products_with_transformer(reactant_smiles)
    elif method == 'rdkit':
        return predict_products_with_rdkit(reactant_smiles)
    else:
        raise ValueError(f"Unknown prediction method: {method}")