"""
Handles energy prediction using a pre-trained GNN surrogate model (SchNet).
"""
import schnetpack as spk
import torch
from ase import Atoms
from schnetpack.interfaces import SpkCalculator

# Global cache for the model to avoid reloading it on every call
MODEL_CACHE = None

def load_surrogate_model():
    """
    Loads the pre-trained SchNet model for the QM9 dataset.
    Caches the model in memory for subsequent calls.
    
    Returns:
        The loaded and evaluated model object.
    """
    global MODEL_CACHE
    if MODEL_CACHE is not None:
        return MODEL_CACHE

    print("Loading pre-trained GNN surrogate model (SchNet on QM9)...")
    try:
        # The QM9 model predicts multiple properties. We are interested in energy.
        model_path = spk.data.fetch_pretrained_model('schnet', 'qm9')
        MODEL_CACHE = torch.load(model_path, map_location=torch.device('cpu'))
        print("Surrogate model loaded successfully.")
        return MODEL_CACHE
    except Exception as e:
        print(f"Error loading surrogate model. Ensure you have an internet connection for the first download.")
        print(f"Details: {e}")
        return None

def predict_energy_with_surrogate(atoms: Atoms) -> float:
    """
    Predicts the electronic energy at 0K (U0) for a given molecule using the surrogate.

    Args:
        atoms: An ASE Atoms object representing the molecule.

    Returns:
        The predicted energy in eV, or float('nan') on failure.
    """
    model = load_surrogate_model()
    if model is None:
        return float('nan')

    # SchNetPack requires a specific calculator interface.
    # We will predict the 'energy' property, which corresponds to U0 in the QM9 dataset.
    calculator = SpkCalculator(
        model,
        energy_key='energy', # This is the standard key for U0 in the QM9 model
        force_key=None      # We explicitly state we are not predicting forces
    )

    # Attach the calculator and get the potential energy
    atoms.calc = calculator
    try:
        # get_potential_energy() triggers the calculation
        predicted_energy_ev = atoms.get_potential_energy()
        print(f"Surrogate predicted energy for {atoms.get_chemical_formula()}: {predicted_energy_ev:.4f} eV")
        return predicted_energy_ev
    except Exception as e:
        print(f"Error during surrogate energy prediction for {atoms.get_chemical_formula()}: {e}")
        return float('nan')